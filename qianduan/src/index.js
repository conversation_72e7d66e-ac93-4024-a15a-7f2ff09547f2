// React 由 JSX 转换自动处理
import React from 'react';
import ReactDOM from 'react-dom/client';
import { StagewiseToolbar } from '@stagewise/toolbar-react';
import ReactPlugin from '@stagewise-plugins/react';
import { useRequest } from 'ahooks';
import init, { chushihuawangguan, get_qingqiu } from './qudong/wangluoqingqiu/wangluoqingqiu.js';
import {
  tiqu_wangzhan_jichuxinxi,
  cunchu_dao_huancun,
  cong_huancun_duqu
} from './gongju/wangzhanjichuxinxi_huancun.js';
import {
  yingyong_wangzhan_jichuxinxi,
  kuaisu_yingyong_mingcheng_tubiao
} from './gongju/wangzhan_dongtai_yingyong.js';
// 导入明暗布局组件
import {
  Minganbuju,
  useShiyongzhuti,
} from './zujian/minganbuju';
// 导入顶部导航栏组件
import { Dingbudaohanglan } from './zujian/dingbudaohanglan';
// 导入路由检测Hook
import { useShifougengluyou } from './zujian/dingbudaohanglan/useLuyoujiance.js';
// 导入预加载动画组件
import Yujiazaizujian, { useYujiazai } from './zujian/yujiazaizujian';

const yuanshiconsole = console.log;
console.log = function(...args) {
  const message = args.join(' ');
  if (message.includes('[AgentProvider]') ||
      message.includes('[PanelsProvider]') ||
      message.includes('[PanelWrapper]') ||
      message.includes('[stagewise]')) {
    return;
  }
  yuanshiconsole.apply(console, args);
};

let chushipromise = null;
let yichushihua = false;
let wasm_yichushihua = false;

async function chushihua_wasm() {
    if (wasm_yichushihua) {
        return;
    }
    await init();
    chushihuawangguan('http://127.0.0.1:8098');
    wasm_yichushihua = true;
}

async function qianduanchushihua() {
  if (yichushihua) {
    return null;
  }
  if (chushipromise) {
    return await chushipromise;
  }

  chushipromise = (async () => {
    try {
      await chushihua_wasm();

      let wangzhan_xinxi = cong_huancun_duqu();

      if (wangzhan_xinxi) {
        console.log('🎯 [初始化] 从缓存加载网站基础信息:', wangzhan_xinxi);
        kuaisu_yingyong_mingcheng_tubiao(wangzhan_xinxi);
        yingyong_wangzhan_jichuxinxi(wangzhan_xinxi);
        yichushihua = true;
        return {
          laiyuan: 'huancun',
          shuju: wangzhan_xinxi
        };
      }

      const xiangying = await get_qingqiu(
        '/jiekou/wangzhanjichuxinxi',
        null,
        false,
        false,
        5000,
        1
      );

      wangzhan_xinxi = tiqu_wangzhan_jichuxinxi(xiangying);
      if (!wangzhan_xinxi) {
        yichushihua = true;
        throw new Error('提取网站基础信息失败');
      }

      console.log('🎯 [初始化] 从网络加载网站基础信息:', wangzhan_xinxi);
      kuaisu_yingyong_mingcheng_tubiao(wangzhan_xinxi);
      cunchu_dao_huancun(wangzhan_xinxi);
      const yingyong_jieguo = yingyong_wangzhan_jichuxinxi(wangzhan_xinxi);

      yichushihua = true;
      return {
        laiyuan: 'wangluo',
        shuju: wangzhan_xinxi,
        yingyong_jieguo: yingyong_jieguo
      };

    } catch (cuowu) {
      console.error('前端初始化失败:', cuowu);
      chushipromise = null;
      throw cuowu;
    }
  })();

  return await chushipromise;
}

// 主题和导航栏监测器组件
function ZhutiheDaohanglanJianceqi({ onZhutiZhunbei, onDaohanglanZhunbei, caidanxiangmu }) {
  const zhuti_zhunbei_ref = React.useRef(false);
  const daohanglan_zhunbei_ref = React.useRef(false);

  // 检测是否为根路由（首页）
  const shifougengluyou = useShifougengluyou();

  // 监听主题系统准备状态
  React.useEffect(() => {
    if (!zhuti_zhunbei_ref.current) {
      // 主题系统在Minganbuju组件渲染后就准备好了
      const timer = setTimeout(() => {
        zhuti_zhunbei_ref.current = true;
        onZhutiZhunbei();
      }, 100); // 给主题系统一点时间初始化

      return () => clearTimeout(timer);
    }
  }, [onZhutiZhunbei]);

  // 监听导航栏准备状态
  React.useEffect(() => {
    if (!daohanglan_zhunbei_ref.current) {
      // 导航栏组件渲染后就准备好了
      const timer = setTimeout(() => {
        daohanglan_zhunbei_ref.current = true;
        onDaohanglanZhunbei();

      }, 200); // 给导航栏组件一点时间渲染

      return () => clearTimeout(timer);
    }
  }, [onDaohanglanZhunbei]);

  return (
    <>
      <StagewiseToolbar config={{ plugins: [ReactPlugin] }} />

      {/* 顶部导航栏 - 在首页和数据页面显示 */}
      <Dingbudaohanglan
        wangzhanmingcheng="RO百科"
        wangzhanlogo="https://pan.new-cdn.com/f/GONUG/yunluo.jpg"
        caidanxiangmu={caidanxiangmu}
        xianshi={shifougengluyou}
      />
    </>
  );
}

function App() {
  // 记录组件开始时间
  const kaishi_shijian_ref = React.useRef(Date.now());



  // 组件准备状态
  const [zujian_zhunbei_zhuangtai, shezhi_zujian_zhunbei_zhuangtai] = React.useState({
    zhuti_zhunbei: false,
    daohanglan_zhunbei: false,
    wasm_zhunbei: false
  });

  // 隐藏初始预加载动画
  React.useEffect(() => {
    const chushiYujiazai = document.getElementById('chushi-yujiazai');
    if (chushiYujiazai) {
      chushiYujiazai.style.display = 'none';
    }
    document.body.classList.add('react-loaded');
  }, []);

  // 预加载状态管理
  const {
    shifouyujiazai,
    tingzhiyujiazai
  } = useYujiazai({
    chushizhuangtai: true, // 初始状态为显示预加载
    zuiduan_xianshi_shijian: 2000, // 最短显示2秒
    onkaishi: () => {
      console.log('🎬 [App] 网站预加载开始', {
        shijian: new Date().toISOString()
      });
    },
    ontingzhi: () => {
      console.log('🎬 [App] 网站预加载结束', {
        shijian: new Date().toISOString()
      });
    }
  });

  // 使用useRequest管理前端初始化
  useRequest(qianduanchushihua, {
    manual: false, // 自动执行
    cacheKey: 'wasm-init', // 缓存key，防止重复初始化
    staleTime: Infinity, // 永不过期，确保只初始化一次
    onSuccess: () => {
      // 标记WASM初始化完成
      shezhi_zujian_zhunbei_zhuangtai(prev => ({
        ...prev,
        wasm_zhunbei: true
      }));
    },
    onError: () => {
      // 即使失败也标记为完成，避免无限等待
      shezhi_zujian_zhunbei_zhuangtai(prev => ({
        ...prev,
        wasm_zhunbei: true
      }));
    }
  });

  // 监听所有组件准备状态，当全部准备好时停止预加载
  React.useEffect(() => {
    const { zhuti_zhunbei, daohanglan_zhunbei, wasm_zhunbei } = zujian_zhunbei_zhuangtai;

    // 检查是否所有组件都准备好了
    if (zhuti_zhunbei && daohanglan_zhunbei && wasm_zhunbei) {


      // 计算已经过去的时间
      const yijing_guoqu_shijian = Date.now() - kaishi_shijian_ref.current;
      const zuiduan_xianshi_shijian = 2000; // 2秒

      // 如果还没到2秒，等待剩余时间
      if (yijing_guoqu_shijian < zuiduan_xianshi_shijian) {
        const shengyu_shijian = zuiduan_xianshi_shijian - yijing_guoqu_shijian;

        setTimeout(() => {
          tingzhiyujiazai();
        }, shengyu_shijian);
      } else {
        // 已经超过2秒，立即停止

        tingzhiyujiazai();
      }
    }
  }, [zujian_zhunbei_zhuangtai, tingzhiyujiazai]);

  // 导航栏配置（首页链接已内置在组件中）
  const caidanxiangmu = [
    { mingcheng: '怪物数据', lianjie: '/guaiwushuju', huoyue: false },
    { mingcheng: '物品数据', lianjie: '/wupinshuju', huoyue: false },
    { mingcheng: '地图数据', lianjie: '/ditushuju', huoyue: false },
    { mingcheng: '技能数据', lianjie: '/jinengshuju', huoyue: false }
  ];





  return (
    <Minganbuju
      qiyongquanjuyangshi={true}
    >
      {/* 预加载动画组件 - 放在最顶层确保立即显示 */}
      <Yujiazaizujian
        xianshi={shifouyujiazai}
        wenzi=""
        xianshi_jindutiao={false}
      />

      <ZhutiheDaohanglanJianceqi
        onZhutiZhunbei={() => {
          shezhi_zujian_zhunbei_zhuangtai(prev => ({
            ...prev,
            zhuti_zhunbei: true
          }));
        }}
        onDaohanglanZhunbei={() => {
          shezhi_zujian_zhunbei_zhuangtai(prev => ({
            ...prev,
            daohanglan_zhunbei: true
          }));
        }}
        caidanxiangmu={caidanxiangmu}
      />

      <AppNeirong />
    </Minganbuju>
  );
}

// 应用内容组件（使用主题）
function AppNeirong() {
  const { shifoianheizuti } = useShiyongzhuti();

  return (
    <div style={{
      paddingTop: '80px', // 为固定导航栏留出空间
      minHeight: '100vh',
      /* 移动端优化 */
      touchAction: 'manipulation',
      WebkitTouchCallout: 'none',
      WebkitUserSelect: 'none',
      userSelect: 'none',
      /* 隐藏滚动条但保持滚动功能 */
      overflowX: 'hidden',
      scrollbarWidth: 'none',
      msOverflowStyle: 'none'
    }}>
      {/* 主要内容区域 */}
      <div style={{
        padding: '20px',
        textAlign: 'center'
      }}>
        <h1 style={{
          userSelect: 'text',
          fontSize: '2.5rem',
          marginBottom: '1rem',
          color: shifoianheizuti ? '#ffffff' : '#1f2937'
        }}>
          欢迎来到 RO百科
        </h1>
        <p style={{
          fontSize: '1.2rem',
          opacity: 0.8,
          maxWidth: '600px',
          margin: '0 auto',
          lineHeight: 1.6
        }}>
          这里是您的仙境传说资源中心，提供丰富的游戏信息和工具。
        </p>
      </div>
    </div>
  );
}

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(<App />);
